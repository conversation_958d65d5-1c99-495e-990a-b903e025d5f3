{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "46,47", "startColumns": "4,4", "startOffsets": "3411,3522", "endColumns": "110,111", "endOffsets": "3517,3629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "74,127,128,129", "startColumns": "4,4,4,4", "startOffsets": "6597,10385,10486,10597", "endColumns": "100,100,110,101", "endOffsets": "6693,10481,10592,10694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8513,8583,8648,8717,8786,8861,8925,9022,9116", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "8578,8643,8712,8781,8856,8920,9017,9111,9184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5347", "endColumns": "142", "endOffsets": "5485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1750,1828,1905,1977,2056,2127,2197,2278,2359,2423,2486,2539,2597,2645,2706,2767,2834,2896,2962,3021,3086,3151,3216,3284,3337,3397,3471,3545", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,78,77,76,71,78,70,69,80,80,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1745,1823,1900,1972,2051,2122,2192,2273,2354,2418,2481,2534,2592,2640,2701,2762,2829,2891,2957,3016,3081,3146,3211,3279,3332,3392,3466,3540,3593"}, "to": {"startLines": "2,11,15,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,537,6698,6780,6860,6943,7030,7124,7192,7256,7346,7437,7502,7570,7630,7698,7777,7855,7932,8004,8083,8154,8224,8305,8386,8450,9189,9242,9300,9348,9409,9470,9537,9599,9665,9724,9789,9854,9919,9987,10040,10100,10174,10248", "endLines": "10,14,18,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,78,77,76,71,78,70,69,80,80,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "331,532,716,6775,6855,6938,7025,7119,7187,7251,7341,7432,7497,7565,7625,7693,7772,7850,7927,7999,8078,8149,8219,8300,8381,8445,8508,9237,9295,9343,9404,9465,9532,9594,9660,9719,9784,9849,9914,9982,10035,10095,10169,10243,10296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,343,480,649,729", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "172,256,338,475,644,724,802"}, "to": {"startLines": "73,126,130,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6525,10301,10699,10781,11098,11267,11347", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "6592,10380,10776,10913,11262,11342,11420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "48,49,50,51,52,53,54,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3634,3734,3838,3939,4042,4144,4249,10997", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3729,3833,3934,4037,4139,4244,4361,11093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,889,980,1072,1165,1262,1363,1456,1551,1645,1736,1827,1906,2013,2114,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,101,111,77,77,90,91,92,96,100,92,94,93,90,90,78,106,100,95,108,101,113,156,102,78", "endOffsets": "211,317,424,514,616,728,806,884,975,1067,1160,1257,1358,1451,1546,1640,1731,1822,1901,2008,2109,2205,2314,2416,2530,2687,2790,2869"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "721,832,938,1045,1135,1237,1349,1427,1505,1596,1688,1781,1878,1979,2072,2167,2261,2352,2443,2522,2629,2730,2826,2935,3037,3151,3308,10918", "endColumns": "110,105,106,89,101,111,77,77,90,91,92,96,100,92,94,93,90,90,78,106,100,95,108,101,113,156,102,78", "endOffsets": "827,933,1040,1130,1232,1344,1422,1500,1591,1683,1776,1873,1974,2067,2162,2256,2347,2438,2517,2624,2725,2821,2930,3032,3146,3303,3406,10992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4366,4473,4641,4764,4876,5021,5142,5250,5490,5640,5748,5902,6026,6165,6318,6378,6444", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "4468,4636,4759,4871,5016,5137,5245,5342,5635,5743,5897,6021,6160,6313,6373,6439,6520"}}]}]}