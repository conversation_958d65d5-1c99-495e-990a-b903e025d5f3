{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,827,904,996,1089,1184,1278,1379,1473,1569,1664,1756,1848,1929,2040,2143,2242,2357,2471,2574,2729,2832", "endColumns": "117,104,106,85,107,119,77,76,91,92,94,93,100,93,95,94,91,91,80,110,102,98,114,113,102,154,102,81", "endOffsets": "218,323,430,516,624,744,822,899,991,1084,1179,1273,1374,1468,1564,1659,1751,1843,1924,2035,2138,2237,2352,2466,2569,2724,2827,2909"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "727,845,950,1057,1143,1251,1371,1449,1526,1618,1711,1806,1900,2001,2095,2191,2286,2378,2470,2551,2662,2765,2864,2979,3093,3196,3351,11166", "endColumns": "117,104,106,85,107,119,77,76,91,92,94,93,100,93,95,94,91,91,80,110,102,98,114,113,102,154,102,81", "endOffsets": "840,945,1052,1138,1246,1366,1444,1521,1613,1706,1801,1895,1996,2090,2186,2281,2373,2465,2546,2657,2760,2859,2974,3088,3191,3346,3449,11243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "48,49,50,51,52,53,54,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3690,3792,3894,3994,4094,4201,4305,11248", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3787,3889,3989,4089,4196,4300,4419,11344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "73,126,130,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6726,10538,10939,11020,11349,11518,11598", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "6793,10623,11015,11161,11513,11593,11670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,448,572,679,842,965,1084,1186,1360,1462,1627,1749,1908,2086,2150,2209", "endColumns": "103,150,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,447,571,678,841,964,1083,1185,1359,1461,1626,1748,1907,2085,2149,2208,2283"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4424,4532,4687,4815,4926,5093,5220,5343,5592,5770,5876,6045,6171,6334,6516,6584,6647", "endColumns": "107,154,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4527,4682,4810,4921,5088,5215,5338,5444,5765,5871,6040,6166,6329,6511,6579,6642,6721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5449", "endColumns": "142", "endOffsets": "5587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1808,1887,1964,2037,2116,2191,2260,2337,2413,2479,2544,2597,2655,2703,2764,2829,2891,2956,3024,3082,3140,3206,3271,3337,3389,3451,3527,3603", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1803,1882,1959,2032,2111,2186,2255,2332,2408,2474,2539,2592,2650,2698,2759,2824,2886,2951,3019,3077,3135,3201,3266,3332,3384,3446,3522,3598,3653"}, "to": {"startLines": "2,11,15,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,536,6901,6990,7078,7158,7251,7344,7417,7484,7586,7684,7752,7819,7884,7953,8032,8111,8188,8261,8340,8415,8484,8561,8637,8703,9424,9477,9535,9583,9644,9709,9771,9836,9904,9962,10020,10086,10151,10217,10269,10331,10407,10483", "endLines": "10,14,18,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "331,531,722,6985,7073,7153,7246,7339,7412,7479,7581,7679,7747,7814,7879,7948,8027,8106,8183,8256,8335,8410,8479,8556,8632,8698,8763,9472,9530,9578,9639,9704,9766,9831,9899,9957,10015,10081,10146,10212,10264,10326,10402,10478,10533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "46,47", "startColumns": "4,4", "startOffsets": "3454,3568", "endColumns": "113,121", "endOffsets": "3563,3685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "74,127,128,129", "startColumns": "4,4,4,4", "startOffsets": "6798,10628,10729,10840", "endColumns": "102,100,110,98", "endOffsets": "6896,10724,10835,10934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8768,8839,8903,8967,9034,9111,9180,9269,9352", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "8834,8898,8962,9029,9106,9175,9264,9347,9419"}}]}]}