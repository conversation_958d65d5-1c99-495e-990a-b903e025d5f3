1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.whatever"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:8:1-63
15-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:8:18-60
16    <!-- Camera permissions -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:3:1-60
17-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:3:18-58
18
19    <uses-feature
19-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:4:1-80
20        android:name="android.hardware.camera"
20-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:4:15-53
21        android:required="true" /> <!-- Microphone permissions -->
21-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:4:54-77
22    <uses-permission android:name="android.permission.RECORD_AUDIO" />
22-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:7:1-67
22-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:7:18-64
23    <uses-permission
23-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:9:1-104
24        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
24-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:9:18-74
25        android:maxSdkVersion="32" />
25-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:9:75-101
26    <uses-permission
26-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:10:1-103
27        android:name="android.permission.READ_EXTERNAL_STORAGE"
27-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:10:18-73
28        android:maxSdkVersion="32" />
28-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:10:74-100
29    <!--
30 Required to query activities that can process text, see:
31         https://developer.android.com/training/package-visibility and
32         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
33
34         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
35    -->
36    <queries>
36-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:50:5-55:15
37        <intent>
37-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:51:9-54:18
38            <action android:name="android.intent.action.PROCESS_TEXT" />
38-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:52:13-72
38-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:52:21-70
39
40            <data android:mimeType="text/plain" />
40-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:53:13-50
40-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:53:19-48
41        </intent>
42    </queries>
43
44    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
44-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
44-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:22-76
45    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
45-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63c8215b4ef0ce806491a9ae657bddeb\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
45-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63c8215b4ef0ce806491a9ae657bddeb\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.whatever.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.whatever.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:icon="@mipmap/ic_launcher"
59        android:label="whatever" >
60        <activity
61            android:name="com.example.whatever.MainActivity"
62            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
63            android:exported="true"
64            android:hardwareAccelerated="true"
65            android:launchMode="singleTop"
66            android:taskAffinity=""
67            android:theme="@style/LaunchTheme"
68            android:windowSoftInputMode="adjustResize" >
69
70            <!--
71                 Specifies an Android theme to apply to this Activity as soon as
72                 the Android process has started. This theme is visible to the user
73                 while the Flutter UI initializes. After that, this theme continues
74                 to determine the Window background behind the Flutter UI.
75            -->
76            <meta-data
77                android:name="io.flutter.embedding.android.NormalTheme"
78                android:resource="@style/NormalTheme" />
79
80            <intent-filter>
81                <action android:name="android.intent.action.MAIN" />
82
83                <category android:name="android.intent.category.LAUNCHER" />
84            </intent-filter>
85        </activity>
86        <!--
87             Don't delete the meta-data below.
88             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
89        -->
90        <meta-data
91            android:name="flutterEmbedding"
92            android:value="2" />
93
94        <service
94-->[:firebase_auth] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
95            android:name="com.google.firebase.components.ComponentDiscoveryService"
95-->[:firebase_auth] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
96            android:directBootAware="true"
96-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
97            android:exported="false" >
97-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
98            <meta-data
98-->[:firebase_auth] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
99                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
99-->[:firebase_auth] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[:firebase_auth] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
101            <meta-data
101-->[:firebase_core] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
102                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
102-->[:firebase_core] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[:firebase_core] C:\Users\<USER>\Documents\projects\pfe\Front-END\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
104            <meta-data
104-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
105                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
105-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
107            <meta-data
107-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
108                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
108-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
110            <meta-data
110-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
111                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
111-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
113        </service>
114
115        <activity
115-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
116            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
116-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
117            android:excludeFromRecents="true"
117-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
118            android:exported="true"
118-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
119            android:launchMode="singleTask"
119-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
120            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
120-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
121            <intent-filter>
121-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
122                <action android:name="android.intent.action.VIEW" />
122-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
122-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
124-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
125                <category android:name="android.intent.category.BROWSABLE" />
125-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
125-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
126
127                <data
127-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:53:13-50
128                    android:host="firebase.auth"
129                    android:path="/"
130                    android:scheme="genericidp" />
131            </intent-filter>
132        </activity>
133        <activity
133-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
134            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
134-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
135            android:excludeFromRecents="true"
135-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
136            android:exported="true"
136-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
137            android:launchMode="singleTask"
137-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
139            <intent-filter>
139-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
140                <action android:name="android.intent.action.VIEW" />
140-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
140-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
142-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
143                <category android:name="android.intent.category.BROWSABLE" />
143-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
143-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\633413bad6a67ccffb521b72ab86b298\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
144
145                <data
145-->C:\Users\<USER>\Documents\projects\pfe\Front-END\android\app\src\main\AndroidManifest.xml:53:13-50
146                    android:host="firebase.auth"
147                    android:path="/"
148                    android:scheme="recaptcha" />
149            </intent-filter>
150        </activity>
151
152        <provider
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
153            android:name="com.google.firebase.provider.FirebaseInitProvider"
153-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
154            android:authorities="com.example.whatever.firebaseinitprovider"
154-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
155            android:directBootAware="true"
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
156            android:exported="false"
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
157            android:initOrder="100" />
157-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
158
159        <service
159-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
160            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
160-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
161            android:enabled="true"
161-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
162            android:exported="false" >
162-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
163            <meta-data
163-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
164                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
164-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
165                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
165-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
166        </service>
167
168        <activity
168-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
169            android:name="androidx.credentials.playservices.HiddenActivity"
169-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
170            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
170-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
171            android:enabled="true"
171-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
172            android:exported="false"
172-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
173            android:fitsSystemWindows="true"
173-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
174            android:theme="@style/Theme.Hidden" >
174-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
175        </activity>
176        <activity
176-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
177            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
177-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
178            android:excludeFromRecents="true"
178-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
179            android:exported="false"
179-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
180            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
180-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
181        <!--
182            Service handling Google Sign-In user revocation. For apps that do not integrate with
183            Google Sign-In, this service will never be started.
184        -->
185        <service
185-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
186            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
186-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
187            android:exported="true"
187-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
188            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
188-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
189            android:visibleToInstantApps="true" />
189-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
190
191        <activity
191-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6aa5fd7f2304b1bbe5c05f658d5472\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
192            android:name="com.google.android.gms.common.api.GoogleApiActivity"
192-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6aa5fd7f2304b1bbe5c05f658d5472\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
193            android:exported="false"
193-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6aa5fd7f2304b1bbe5c05f658d5472\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
194-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6aa5fd7f2304b1bbe5c05f658d5472\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
195
196        <uses-library
196-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
197            android:name="androidx.window.extensions"
197-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
198            android:required="false" />
198-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
199        <uses-library
199-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
200            android:name="androidx.window.sidecar"
200-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
201            android:required="false" />
201-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
202
203        <meta-data
203-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
204            android:name="com.google.android.gms.version"
204-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
205            android:value="@integer/google_play_services_version" />
205-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
206
207        <provider
207-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
208            android:name="androidx.startup.InitializationProvider"
208-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
209            android:authorities="com.example.whatever.androidx-startup"
209-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
210            android:exported="false" >
210-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
211            <meta-data
211-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
212-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
213                android:value="androidx.startup" />
213-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
214            <meta-data
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
216                android:value="androidx.startup" />
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
217        </provider>
218
219        <receiver
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
220            android:name="androidx.profileinstaller.ProfileInstallReceiver"
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
221            android:directBootAware="false"
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
222            android:enabled="true"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
223            android:exported="true"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
224            android:permission="android.permission.DUMP" >
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
226                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
227            </intent-filter>
228            <intent-filter>
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
229                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
230            </intent-filter>
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
232                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
233            </intent-filter>
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
235                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
236            </intent-filter>
237        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
238        <activity
238-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc78addb618cc9a88505262f8a23e42d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
239            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
239-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc78addb618cc9a88505262f8a23e42d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
240            android:exported="false"
240-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc78addb618cc9a88505262f8a23e42d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
241            android:stateNotNeeded="true"
241-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc78addb618cc9a88505262f8a23e42d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
242            android:theme="@style/Theme.PlayCore.Transparent" />
242-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc78addb618cc9a88505262f8a23e42d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
243    </application>
244
245</manifest>
