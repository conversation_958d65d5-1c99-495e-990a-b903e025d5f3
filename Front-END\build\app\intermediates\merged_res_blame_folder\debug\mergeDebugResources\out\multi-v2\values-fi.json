{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4375,4486,4639,4770,4876,5019,5145,5261,5518,5659,5765,5914,6040,6188,6327,6393,6463", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4481,4634,4765,4871,5014,5140,5256,5363,5654,5760,5909,6035,6183,6322,6388,6458,6541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "74,127,128,129", "startColumns": "4,4,4,4", "startOffsets": "6616,10514,10615,10724", "endColumns": "102,100,108,98", "endOffsets": "6714,10610,10719,10818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "46,47", "startColumns": "4,4", "startOffsets": "3412,3524", "endColumns": "111,116", "endOffsets": "3519,3636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,897,988,1080,1175,1269,1364,1457,1553,1652,1743,1837,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,79,90,91,94,93,94,92,95,98,90,93,78,106,100,96,105,99,97,149,99,79", "endOffsets": "208,308,417,503,608,726,812,892,983,1075,1170,1264,1359,1452,1548,1647,1738,1832,1911,2018,2119,2216,2322,2422,2520,2670,2770,2850"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,850,950,1059,1145,1250,1368,1454,1534,1625,1717,1812,1906,2001,2094,2190,2289,2380,2474,2553,2660,2761,2858,2964,3064,3162,3312,11036", "endColumns": "107,99,108,85,104,117,85,79,90,91,94,93,94,92,95,98,90,93,78,106,100,96,105,99,97,149,99,79", "endOffsets": "845,945,1054,1140,1245,1363,1449,1529,1620,1712,1807,1901,1996,2089,2185,2284,2375,2469,2548,2655,2756,2853,2959,3059,3157,3307,3407,11111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5368", "endColumns": "149", "endOffsets": "5513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,692,783,876,957,1053,1145,1216,1283,1372,1459,1527,1592,1655,1727,1815,1899,1985,2062,2144,2216,2287,2380,2469,2534,2598,2651,2709,2757,2818,2886,2958,3027,3099,3166,3221,3286,3352,3418,3470,3531,3606,3681", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,87,83,85,76,81,71,70,92,88,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,778,871,952,1048,1140,1211,1278,1367,1454,1522,1587,1650,1722,1810,1894,1980,2057,2139,2211,2282,2375,2464,2529,2593,2646,2704,2752,2813,2881,2953,3022,3094,3161,3216,3281,3347,3413,3465,3526,3601,3676,3733"}, "to": {"startLines": "2,11,15,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,541,6719,6810,6903,6984,7080,7172,7243,7310,7399,7486,7554,7619,7682,7754,7842,7926,8012,8089,8171,8243,8314,8407,8496,8561,9282,9335,9393,9441,9502,9570,9642,9711,9783,9850,9905,9970,10036,10102,10154,10215,10290,10365", "endLines": "10,14,18,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,87,83,85,76,81,71,70,92,88,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "332,536,737,6805,6898,6979,7075,7167,7238,7305,7394,7481,7549,7614,7677,7749,7837,7921,8007,8084,8166,8238,8309,8402,8491,8556,8620,9330,9388,9436,9497,9565,9637,9706,9778,9845,9900,9965,10031,10097,10149,10210,10285,10360,10417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "48,49,50,51,52,53,54,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3641,3737,3839,3937,4042,4147,4259,11116", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3732,3834,3932,4037,4142,4254,4370,11212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "73,126,130,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6546,10422,10823,10901,11217,11386,11476", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "6611,10509,10896,11031,11381,11471,11553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8625,8690,8749,8811,8878,8955,9025,9119,9211", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "8685,8744,8806,8873,8950,9020,9114,9206,9277"}}]}]}