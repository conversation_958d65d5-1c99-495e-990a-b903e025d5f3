{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8965,9046,9110,9175,9247,9325,9405,9495,9588", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "9041,9105,9170,9242,9320,9400,9490,9583,9657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,918,1009,1101,1196,1290,1391,1484,1579,1673,1764,1856,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,80,90,91,94,93,100,92,94,93,90,91,81,111,107,99,113,105,105,163,102,82", "endOffsets": "221,325,438,522,626,747,832,913,1004,1096,1191,1285,1386,1479,1574,1668,1759,1851,1933,2045,2153,2253,2367,2473,2579,2743,2846,2929"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,1023,1127,1240,1324,1428,1549,1634,1715,1806,1898,1993,2087,2188,2281,2376,2470,2561,2653,2735,2847,2955,3055,3169,3275,3381,3545,11410", "endColumns": "120,103,112,83,103,120,84,80,90,91,94,93,100,92,94,93,90,91,81,111,107,99,113,105,105,163,102,82", "endOffsets": "1018,1122,1235,1319,1423,1544,1629,1710,1801,1893,1988,2082,2183,2276,2371,2465,2556,2648,2730,2842,2950,3050,3164,3270,3376,3540,3643,11488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4606,4713,4873,5002,5111,5262,5394,5517,5770,5932,6042,6201,6333,6479,6645,6716,6783", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "4708,4868,4997,5106,5257,5389,5512,5621,5927,6037,6196,6328,6474,6640,6711,6778,6860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "75,128,132,133,136,137,138", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6865,10788,11196,11271,11594,11763,11850", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "6931,10869,11266,11405,11758,11845,11926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}, "to": {"startLines": "48,49", "startColumns": "4,4", "startOffsets": "3648,3758", "endColumns": "109,120", "endOffsets": "3753,3874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,379", "endColumns": "106,101,114,104", "endOffsets": "157,259,374,479"}, "to": {"startLines": "76,129,130,131", "startColumns": "4,4,4,4", "startOffsets": "6936,10874,10976,11091", "endColumns": "106,101,114,104", "endOffsets": "7038,10971,11086,11191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "50,51,52,53,54,55,56,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3879,3977,4079,4179,4278,4380,4489,11493", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3972,4074,4174,4273,4375,4484,4601,11589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,1998,2083,2161,2237,2317,2386,2462,2556,2646,2711,2774,2827,2885,2933,2994,3058,3128,3193,3262,3323,3381,3447,3511,3577,3629,3691,3767,3843", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,1993,2078,2156,2232,2312,2381,2457,2551,2641,2706,2769,2822,2880,2928,2989,3053,3123,3188,3257,3318,3376,3442,3506,3572,3624,3686,3762,3838,3895"}, "to": {"startLines": "2,11,16,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,632,7043,7130,7219,7307,7403,7499,7574,7640,7739,7836,7907,7972,8035,8104,8189,8274,8352,8428,8508,8577,8653,8747,8837,8902,9662,9715,9773,9821,9882,9946,10016,10081,10150,10211,10269,10335,10399,10465,10517,10579,10655,10731", "endLines": "10,15,20,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "331,627,897,7125,7214,7302,7398,7494,7569,7635,7734,7831,7902,7967,8030,8099,8184,8269,8347,8423,8503,8572,8648,8742,8832,8897,8960,9710,9768,9816,9877,9941,10011,10076,10145,10206,10264,10330,10394,10460,10512,10574,10650,10726,10783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "5626", "endColumns": "143", "endOffsets": "5765"}}]}]}