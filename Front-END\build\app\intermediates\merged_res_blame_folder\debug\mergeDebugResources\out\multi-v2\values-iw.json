{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "78,131,132,133", "startColumns": "4,4,4,4", "startOffsets": "6643,10307,10407,10513", "endColumns": "90,99,105,101", "endOffsets": "6729,10402,10508,10610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,115", "endOffsets": "159,275"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3581,3690", "endColumns": "108,115", "endOffsets": "3685,3801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8515,8580,8639,8706,8771,8845,8907,8987,9067", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "8575,8634,8701,8766,8840,8902,8982,9062,9124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,875,954,1032,1108,1193,1277,1339,1401,1490,1576,1641,1705,1768,1836,1913,1997,2078,2149,2226,2295,2356,2443,2529,2593,2656,2710,2781,2829,2890,2949,3016,3077,3140,3201,3258,3324,3388,3454,3506,3560,3628,3696", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,949,1027,1103,1188,1272,1334,1396,1485,1571,1636,1700,1763,1831,1908,1992,2073,2144,2221,2290,2351,2438,2524,2588,2651,2705,2776,2824,2885,2944,3011,3072,3135,3196,3253,3319,3383,3449,3501,3555,3623,3691,3745"}, "to": {"startLines": "2,11,17,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,632,6734,6813,6891,6967,7052,7136,7198,7260,7349,7435,7500,7564,7627,7695,7772,7856,7937,8008,8085,8154,8215,8302,8388,8452,9129,9183,9254,9302,9363,9422,9489,9550,9613,9674,9731,9797,9861,9927,9979,10033,10101,10169", "endLines": "10,16,22,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "329,627,920,6808,6886,6962,7047,7131,7193,7255,7344,7430,7495,7559,7622,7690,7767,7851,7932,8003,8080,8149,8210,8297,8383,8447,8510,9178,9249,9297,9358,9417,9484,9545,9608,9669,9726,9792,9856,9922,9974,10028,10096,10164,10218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "67", "startColumns": "4", "startOffsets": "5468", "endColumns": "117", "endOffsets": "5581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "59,60,61,62,63,64,65,66,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4503,4606,4760,4885,4989,5128,5253,5365,5586,5722,5826,5971,6094,6228,6373,6433,6493", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "4601,4755,4880,4984,5123,5248,5360,5463,5717,5821,5966,6089,6223,6368,6428,6488,6569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,258,334,459,628,709", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "169,253,329,454,623,704,783"}, "to": {"startLines": "77,130,134,135,138,139,140", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6574,10223,10615,10691,10998,11167,11248", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "6638,10302,10686,10811,11162,11243,11322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "52,53,54,55,56,57,58,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3806,3900,4002,4099,4196,4297,4397,10897", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3895,3997,4094,4191,4292,4392,4498,10993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,878,969,1062,1156,1250,1351,1444,1539,1632,1723,1815,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,78,90,92,93,93,100,92,94,92,90,91,79,104,102,97,104,101,101,153,96,80", "endOffsets": "205,305,413,497,599,715,794,873,964,1057,1151,1245,1346,1439,1534,1627,1718,1810,1890,1995,2098,2196,2301,2403,2505,2659,2756,2837"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1030,1130,1238,1322,1424,1540,1619,1698,1789,1882,1976,2070,2171,2264,2359,2452,2543,2635,2715,2820,2923,3021,3126,3228,3330,3484,10816", "endColumns": "104,99,107,83,101,115,78,78,90,92,93,93,100,92,94,92,90,91,79,104,102,97,104,101,101,153,96,80", "endOffsets": "1025,1125,1233,1317,1419,1535,1614,1693,1784,1877,1971,2065,2166,2259,2354,2447,2538,2630,2710,2815,2918,3016,3121,3223,3325,3479,3576,10892"}}]}]}