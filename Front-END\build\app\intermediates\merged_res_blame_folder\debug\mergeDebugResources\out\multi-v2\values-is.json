{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,119", "endOffsets": "164,284"}, "to": {"startLines": "46,47", "startColumns": "4,4", "startOffsets": "3413,3527", "endColumns": "113,119", "endOffsets": "3522,3642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4364,4470,4624,4749,4858,4999,5124,5233,5467,5621,5727,5884,6010,6152,6306,6370,6433", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "4465,4619,4744,4853,4994,5119,5228,5333,5616,5722,5879,6005,6147,6301,6365,6428,6507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "74,127,128,129", "startColumns": "4,4,4,4", "startOffsets": "6582,10369,10470,10576", "endColumns": "103,100,105,100", "endOffsets": "6681,10465,10571,10672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8535,8602,8661,8720,8786,8862,8925,9014,9096", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "8597,8656,8715,8781,8857,8920,9009,9091,9160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "48,49,50,51,52,53,54,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3647,3742,3849,3946,4046,4149,4253,10970", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3737,3844,3941,4041,4144,4248,4359,11066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5338", "endColumns": "128", "endOffsets": "5462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1772,1853,1932,2007,2088,2161,2230,2312,2393,2458,2538,2591,2652,2702,2763,2822,2892,2955,3017,3081,3141,3207,3272,3342,3394,3454,3528,3602", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,78,80,78,74,80,72,68,81,80,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1767,1848,1927,2002,2083,2156,2225,2307,2388,2453,2533,2586,2647,2697,2758,2817,2887,2950,3012,3076,3136,3202,3267,3337,3389,3449,3523,3597,3650"}, "to": {"startLines": "2,11,15,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,538,6686,6771,6855,6932,7021,7118,7187,7251,7342,7433,7496,7560,7622,7690,7769,7850,7929,8004,8085,8158,8227,8309,8390,8455,9165,9218,9279,9329,9390,9449,9519,9582,9644,9708,9768,9834,9899,9969,10021,10081,10155,10229", "endLines": "10,14,18,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,78,80,78,74,80,72,68,81,80,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "333,533,734,6766,6850,6927,7016,7113,7182,7246,7337,7428,7491,7555,7617,7685,7764,7845,7924,7999,8080,8153,8222,8304,8385,8450,8530,9213,9274,9324,9385,9444,9514,9577,9639,9703,9763,9829,9894,9964,10016,10076,10150,10224,10277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,2854"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,839,936,1048,1133,1234,1348,1429,1509,1600,1692,1785,1879,1986,2079,2174,2269,2360,2454,2534,2644,2751,2848,2957,3057,3160,3315,10890", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "834,931,1043,1128,1229,1343,1424,1504,1595,1687,1780,1874,1981,2074,2169,2264,2355,2449,2529,2639,2746,2843,2952,3052,3155,3310,3408,10965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,475,644,727", "endColumns": "69,86,77,134,168,82,79", "endOffsets": "170,257,335,470,639,722,802"}, "to": {"startLines": "73,126,130,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6512,10282,10677,10755,11071,11240,11323", "endColumns": "69,86,77,134,168,82,79", "endOffsets": "6577,10364,10750,10885,11235,11318,11398"}}]}]}