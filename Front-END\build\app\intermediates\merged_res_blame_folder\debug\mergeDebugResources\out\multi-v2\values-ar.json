{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2395,2471,2556,2622,2705,2777,2849,2937,3024,3088,3151,3204,3275,3330,3391,3449,3523,3587,3651,3711,3776,3840,3902,3968,4020,4077,4148,4219", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,74,75,84,65,82,71,71,87,86,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2390,2466,2551,2617,2700,2772,2844,2932,3019,3083,3146,3199,3270,3325,3386,3444,3518,3582,3646,3706,3771,3835,3897,3963,4015,4072,4143,4214,4270"}, "to": {"startLines": "2,11,19,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,399,899,7226,7305,7383,7459,7553,7645,7719,7784,7876,7966,8036,8100,8163,8232,8307,8383,8468,8534,8617,8689,8761,8849,8936,9000,9759,9812,9883,9938,9999,10057,10131,10195,10259,10319,10384,10448,10510,10576,10628,10685,10756,10827", "endLines": "10,18,26,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,74,75,84,65,82,71,71,87,86,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "394,894,1359,7300,7378,7454,7548,7640,7714,7779,7871,7961,8031,8095,8158,8227,8302,8378,8463,8529,8612,8684,8756,8844,8931,8995,9058,9807,9878,9933,9994,10052,10126,10190,10254,10314,10379,10443,10505,10571,10623,10680,10751,10822,10878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "56,57,58,59,60,61,62,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4251,4344,4446,4541,4644,4747,4849,11584", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "4339,4441,4536,4639,4742,4844,4958,11680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1063,1155,1249,1350,1443,1538,1631,1722,1816,1894,1999,2097,2195,2303,2403,2506,2661,2758", "endColumns": "107,103,106,81,100,113,79,78,90,91,91,93,100,92,94,92,90,93,77,104,97,97,107,99,102,154,96,80", "endOffsets": "208,312,419,501,602,716,796,875,966,1058,1150,1244,1345,1438,1533,1626,1717,1811,1889,1994,2092,2190,2298,2398,2501,2656,2753,2834"}, "to": {"startLines": "27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1364,1472,1576,1683,1765,1866,1980,2060,2139,2230,2322,2414,2508,2609,2702,2797,2890,2981,3075,3153,3258,3356,3454,3562,3662,3765,3920,11503", "endColumns": "107,103,106,81,100,113,79,78,90,91,91,93,100,92,94,92,90,93,77,104,97,97,107,99,102,154,96,80", "endOffsets": "1467,1571,1678,1760,1861,1975,2055,2134,2225,2317,2409,2503,2604,2697,2792,2885,2976,3070,3148,3253,3351,3449,3557,3657,3760,3915,4012,11579"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "63,64,65,66,67,68,69,70,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4963,5067,5211,5333,5438,5576,5704,5815,6047,6184,6288,6438,6560,6699,6845,6909,6975", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "5062,5206,5328,5433,5571,5699,5810,5912,6179,6283,6433,6555,6694,6840,6904,6970,7054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "82,135,136,137", "startColumns": "4,4,4,4", "startOffsets": "7126,10976,11074,11182", "endColumns": "99,97,107,101", "endOffsets": "7221,11069,11177,11279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "81,134,138,139,142,143,144", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7059,10883,11284,11370,11685,11854,11936", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "7121,10971,11365,11498,11849,11931,12011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "71", "startColumns": "4", "startOffsets": "5917", "endColumns": "129", "endOffsets": "6042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "4017,4132", "endColumns": "114,118", "endOffsets": "4127,4246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9063,9128,9187,9254,9316,9398,9479,9580,9675", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "9123,9182,9249,9311,9393,9474,9575,9670,9754"}}]}]}