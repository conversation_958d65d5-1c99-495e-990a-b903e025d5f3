{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3754,3863", "endColumns": "108,121", "endOffsets": "3858,3980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "78,131,132,133", "startColumns": "4,4,4,4", "startOffsets": "7003,10964,11068,11180", "endColumns": "105,103,111,101", "endOffsets": "7104,11063,11175,11277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "67", "startColumns": "4", "startOffsets": "5710", "endColumns": "139", "endOffsets": "5845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9053,9121,9181,9245,9309,9384,9465,9564,9655", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "9116,9176,9240,9304,9379,9460,9559,9650,9724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2108,2189,2269,2346,2426,2499,2579,2675,2769,2837,2902,2955,3013,3061,3122,3192,3261,3324,3389,3452,3509,3585,3654,3728,3780,3843,3920,3997", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2103,2184,2264,2341,2421,2494,2574,2670,2764,2832,2897,2950,3008,3056,3117,3187,3256,3319,3384,3447,3504,3580,3649,3723,3775,3838,3915,3992,4046"}, "to": {"startLines": "2,11,17,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,373,695,7109,7196,7284,7367,7465,7566,7649,7714,7811,7905,7976,8046,8110,8178,8259,8340,8420,8497,8577,8650,8730,8826,8920,8988,9729,9782,9840,9888,9949,10019,10088,10151,10216,10279,10336,10412,10481,10555,10607,10670,10747,10824", "endLines": "10,16,22,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "368,690,1003,7191,7279,7362,7460,7561,7644,7709,7806,7900,7971,8041,8105,8173,8254,8335,8415,8492,8572,8645,8725,8821,8915,8983,9048,9777,9835,9883,9944,10014,10083,10146,10211,10274,10331,10407,10476,10550,10602,10665,10742,10819,10873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,343,484,653,741", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "171,257,338,479,648,736,820"}, "to": {"startLines": "77,130,134,135,138,139,140", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6932,10878,11282,11363,11688,11857,11945", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "6998,10959,11358,11499,11852,11940,12024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "59,60,61,62,63,64,65,66,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4708,4814,4979,5108,5215,5362,5492,5606,5850,6008,6115,6282,6412,6564,6716,6786,6848", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "4809,4974,5103,5210,5357,5487,5601,5705,6003,6110,6277,6407,6559,6711,6781,6843,6927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "52,53,54,55,56,57,58,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3985,4082,4184,4282,4386,4489,4591,11587", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "4077,4179,4277,4381,4484,4586,4703,11683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,896,988,1081,1176,1270,1366,1460,1556,1656,1748,1840,1923,2031,2139,2239,2352,2460,2568,2751,2851", "endColumns": "111,101,107,86,102,118,80,78,91,92,94,93,95,93,95,99,91,91,82,107,107,99,112,107,107,182,99,82", "endOffsets": "212,314,422,509,612,731,812,891,983,1076,1171,1265,1361,1455,1551,1651,1743,1835,1918,2026,2134,2234,2347,2455,2563,2746,2846,2929"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1008,1120,1222,1330,1417,1520,1639,1720,1799,1891,1984,2079,2173,2269,2363,2459,2559,2651,2743,2826,2934,3042,3142,3255,3363,3471,3654,11504", "endColumns": "111,101,107,86,102,118,80,78,91,92,94,93,95,93,95,99,91,91,82,107,107,99,112,107,107,182,99,82", "endOffsets": "1115,1217,1325,1412,1515,1634,1715,1794,1886,1979,2074,2168,2264,2358,2454,2554,2646,2738,2821,2929,3037,3137,3250,3358,3466,3649,3749,11582"}}]}]}