from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Form, Request
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uuid
import os
import asyncio
import edge_tts
import logging
import json
from typing import Optional
from datetime import datetime

# Import our custom STT engine
from Modules.Speech.stt_engine import SpeechToText

# Configure logging with UTF-8 encoding
import sys

# Create UTF-8 file handler
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("ocularvoice_server.log", encoding='utf-8')
    ]
)
logger = logging.getLogger("ocularvoice")

# Initialize FastAPI app
app = FastAPI(
    title="OcularVoice API",
    description="API for OcularVoice application - voice, text, and image processing",
    version="1.0.0"
)

# Add CORS middleware to allow requests from the Flutter app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Mount static files for serving audio responses
app.mount("/responses", StaticFiles(directory="responses"), name="responses")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Initialize our custom STT engine with Whisper model
stt_engine = SpeechToText(model_size="base")
OUTPUT_TTS_FILE = "response.mp3"
UPLOAD_DIR = "uploads"

# Create uploads directory if it doesn't exist
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Simple in-memory chat history (in production, use a database)
chat_history = {}

@app.get("/")
async def root():
    """Root endpoint to check if the server is running"""
    return {"message": "OcularVoice API is running", "status": "ok"}

@app.get("/speech/tts/voice-options")
async def get_voice_options():
    """
    Get voice options organized by language and gender for the Flutter app.
    """
    try:
        voice_options = {
            "en": {
                "male": "en-US-GuyNeural",
                "female": "en-US-JennyNeural"
            },
            "fr": {
                "male": "fr-FR-HenriNeural",
                "female": "fr-FR-DeniseNeural"
            },
            "ar": {
                "male": "ar-SA-HamedNeural",
                "female": "ar-SA-ZariyahNeural"
            }
        }

        return {
            "success": True,
            "data": {
                "voice_options": voice_options
            }
        }
    except Exception as e:
        logger.error(f"Error getting voice options: {str(e)}")
        return {
            "success": False,
            "error": f"Error getting voice options: {str(e)}"
        }

@app.post("/voice-chat/")
async def voice_chat(
    audio: UploadFile = File(...),
    session_id: str = Form(None),
    voice_language: str = Form("en"),
    voice_gender: str = Form("male")
):
    """
    Process voice input and return JSON response with transcription
    """
    try:
        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        # Save uploaded file with original extension
        original_filename = audio.filename
        file_ext = os.path.splitext(original_filename)[1] if original_filename else ".m4a"
        temp_filename = os.path.join(UPLOAD_DIR, f"{uuid.uuid4()}{file_ext}")

        logger.info(f"Saving uploaded file with extension {file_ext} to {temp_filename}")

        with open(temp_filename, "wb") as f:
            f.write(await audio.read())

        logger.info(f"Processing voice input for session {session_id}")

        # Transcribe audio using our custom STT engine with Whisper
        result = stt_engine.transcribe(temp_filename)

        # Check for errors
        if "error" in result:
            logger.error(f"Transcription error: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        text = result["text"]
        language = result.get("language", "en")

        # Log the user's message
        logger.info(f"User said: {text} (detected language: {language})")

        # Store in chat history
        if session_id not in chat_history:
            chat_history[session_id] = []

        chat_history[session_id].append({
            "role": "user",
            "content": text,
            "timestamp": datetime.now().isoformat()
        })

        # Generate response (placeholder - integrate with a real AI model)
        reply_text = f"I received your voice message. You said: {text}"

        # Store response in chat history
        chat_history[session_id].append({
            "role": "assistant",
            "content": reply_text,
            "timestamp": datetime.now().isoformat()
        })

        # Generate TTS response and save to responses folder
        response_filename = os.path.join("responses", f"response_{session_id}_{uuid.uuid4()}.mp3")

        # Use appropriate voice based on user preferences and detected language
        voice_options = {
            "en": {
                "male": "en-US-GuyNeural",
                "female": "en-US-JennyNeural"
            },
            "fr": {
                "male": "fr-FR-HenriNeural",
                "female": "fr-FR-DeniseNeural"
            },
            "ar": {
                "male": "ar-SA-HamedNeural",
                "female": "ar-SA-ZariyahNeural"
            }
        }

        # Use voice_language preference if provided, otherwise use detected language
        lang_to_use = voice_language if voice_language in voice_options else language
        if lang_to_use not in voice_options:
            lang_to_use = "en"  # fallback to English

        voice_to_use = voice_options[lang_to_use].get(voice_gender, voice_options[lang_to_use]["male"])

        # Generate TTS
        communicate = edge_tts.Communicate(reply_text, voice_to_use)
        await communicate.save(response_filename)

        logger.info(f"TTS response saved to {response_filename}")

        # Return JSON response with transcription and other data
        response_data = {
            "success": True,
            "session_id": session_id,
            "transcription": text,
            "language": language,
            "response": reply_text,
            "audio_file": response_filename,
            "voice_used": voice_to_use
        }

        # Cleanup
        os.remove(temp_filename)

        # Use JSONResponse with explicit charset for UTF-8
        return JSONResponse(
            content=response_data,
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except Exception as e:
        logger.error(f"Error processing voice chat: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing voice: {str(e)}")

@app.post("/text-chat/")
async def text_chat(request: Request):
    """
    Process text input and return text response
    """
    try:
        data = await request.json()
        message = data.get("message")
        session_id = data.get("session_id")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        logger.info(f"Processing text chat for session {session_id}: {message}")

        # Store in chat history
        if session_id not in chat_history:
            chat_history[session_id] = []

        chat_history[session_id].append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })

        # Generate response - this is where you would integrate with a real AI model
        # For now, we'll create a more intelligent-sounding response
        if "image" in message.lower():
            reply_text = "I can analyze images for you. Please upload an image or describe what you're looking for."
        elif "voice" in message.lower() or "audio" in message.lower() or "speak" in message.lower():
            reply_text = "I can process voice messages and convert them to text. Feel free to use the microphone button to record your message."
        elif "hello" in message.lower() or "hi" in message.lower() or "hey" in message.lower():
            reply_text = "Hello! I'm OcularVoice, your AI assistant. How can I help you today?"
        elif "?" in message:
            reply_text = f"That's an interesting question. Based on my understanding, I would say that {message.replace('?', '.')} But I'd need more context to give you a more detailed answer."
        else:
            reply_text = f"I understand you're saying: '{message}'. How can I assist you further with this?"

        # Store response in chat history
        chat_history[session_id].append({
            "role": "assistant",
            "content": reply_text,
            "timestamp": datetime.now().isoformat()
        })

        # Return JSON response
        return {
            "response": reply_text,
            "session_id": session_id
        }

    except Exception as e:
        logger.error(f"Error processing text chat: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing text: {str(e)}")

@app.post("/image-analysis/")
async def image_analysis(
    image: UploadFile = File(...),
    message: Optional[str] = Form(None),
    session_id: Optional[str] = Form(None)
):
    """
    Process image and optional text message for analysis
    """
    try:
        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        # Save uploaded image
        image_filename = os.path.join(UPLOAD_DIR, f"{uuid.uuid4()}_{image.filename}")
        with open(image_filename, "wb") as f:
            f.write(await image.read())

        logger.info(f"Processing image analysis for session {session_id}")

        # Store in chat history
        if session_id not in chat_history:
            chat_history[session_id] = []

        # Add user message with image reference
        user_message = {
            "role": "user",
            "content": message if message else "Please analyze this image",
            "image": image_filename,
            "timestamp": datetime.now().isoformat()
        }
        chat_history[session_id].append(user_message)

        # Generate response (placeholder - integrate with a real image analysis model)
        reply_text = "I've received your image. This is a placeholder response for image analysis. In a production environment, this would use computer vision to analyze the image content."

        # Store response in chat history
        chat_history[session_id].append({
            "role": "assistant",
            "content": reply_text,
            "timestamp": datetime.now().isoformat()
        })

        # Return JSON response
        return {
            "response": reply_text,
            "session_id": session_id
        }

    except Exception as e:
        logger.error(f"Error processing image analysis: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")

@app.get("/chat-history/{session_id}")
async def get_chat_history(session_id: str):
    """
    Get chat history for a specific session
    """
    if session_id not in chat_history:
        return {"messages": []}

    return {"messages": chat_history[session_id]}

@app.delete("/chat-history/{session_id}")
async def clear_chat_history(session_id: str):
    """
    Clear chat history for a specific session
    """
    if session_id in chat_history:
        del chat_history[session_id]

    return {"status": "success", "message": f"Chat history for session {session_id} cleared"}
