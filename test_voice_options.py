#!/usr/bin/env python3
"""
Test script to verify the voice-options endpoint is working correctly.
"""
import requests
import json

def test_voice_options():
    """Test the voice-options endpoint"""
    url = "http://localhost:8002/speech/tts/voice-options"
    
    try:
        print(f"Testing endpoint: {url}")
        response = requests.get(url)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response JSON:")
            print(json.dumps(data, indent=2))
            
            # Verify the structure
            if data.get("success") and "voice_options" in data.get("data", {}):
                voice_options = data["data"]["voice_options"]
                print("\n✅ Voice options endpoint is working correctly!")
                print(f"Available languages: {list(voice_options.keys())}")
                
                for lang, voices in voice_options.items():
                    print(f"  {lang}: {list(voices.keys())}")
                    
                return True
            else:
                print("❌ Response structure is incorrect")
                return False
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the server. Make sure it's running on localhost:8002")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_voice_options()
