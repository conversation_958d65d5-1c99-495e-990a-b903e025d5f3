{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4476,4585,4750,4885,4996,5163,5298,5417,5664,5833,5945,6120,6258,6415,6581,6651,6710", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "4580,4745,4880,4991,5158,5293,5412,5521,5828,5940,6115,6253,6410,6576,6646,6705,6778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,706,796,887,970,1057,1145,1225,1290,1394,1499,1577,1651,1715,1783,1867,1945,2035,2112,2204,2276,2357,2448,2537,2603,2671,2725,2785,2833,2894,2966,3034,3097,3173,3238,3296,3367,3432,3503,3555,3614,3695,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,83,77,89,76,91,71,80,90,88,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "282,495,701,791,882,965,1052,1140,1220,1285,1389,1494,1572,1646,1710,1778,1862,1940,2030,2107,2199,2271,2352,2443,2532,2598,2666,2720,2780,2828,2889,2961,3029,3092,3168,3233,3291,3362,3427,3498,3550,3609,3690,3771,3828"}, "to": {"startLines": "2,11,15,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,550,6966,7056,7147,7230,7317,7405,7485,7550,7654,7759,7837,7911,7975,8043,8127,8205,8295,8372,8464,8536,8617,8708,8797,8863,9618,9672,9732,9780,9841,9913,9981,10044,10120,10185,10243,10314,10379,10450,10502,10561,10642,10723", "endLines": "10,14,18,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,83,77,89,76,91,71,80,90,88,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "332,545,751,7051,7142,7225,7312,7400,7480,7545,7649,7754,7832,7906,7970,8038,8122,8200,8290,8367,8459,8531,8612,8703,8792,8858,8926,9667,9727,9775,9836,9908,9976,10039,10115,10180,10238,10309,10374,10445,10497,10556,10637,10718,10775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,2932"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "756,876,982,1087,1173,1283,1404,1484,1562,1653,1745,1840,1934,2035,2128,2223,2331,2422,2513,2595,2709,2817,2917,3031,3138,3246,3406,11418", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "871,977,1082,1168,1278,1399,1479,1557,1648,1740,1835,1929,2030,2123,2218,2326,2417,2508,2590,2704,2812,2912,3026,3133,3241,3401,3500,11496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,189,253,327,405,478,575,666", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "121,184,248,322,400,473,570,661,737"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8931,9002,9065,9129,9203,9281,9354,9451,9542", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "8997,9060,9124,9198,9276,9349,9446,9537,9613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,345,485,654,736", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "172,260,340,480,649,731,809"}, "to": {"startLines": "73,126,130,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6783,10780,11198,11278,11602,11771,11853", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "6850,10863,11273,11413,11766,11848,11926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "48,49,50,51,52,53,54,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3735,3832,3942,4044,4145,4252,4357,11501", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3827,3937,4039,4140,4247,4352,4471,11597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5526", "endColumns": "137", "endOffsets": "5659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "74,127,128,129", "startColumns": "4,4,4,4", "startOffsets": "6855,10868,10976,11088", "endColumns": "110,107,111,109", "endOffsets": "6961,10971,11083,11193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,119", "endOffsets": "160,280"}, "to": {"startLines": "46,47", "startColumns": "4,4", "startOffsets": "3505,3615", "endColumns": "109,119", "endOffsets": "3610,3730"}}]}]}