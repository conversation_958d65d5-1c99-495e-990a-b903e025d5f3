{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "50,51,52,53,54,55,56,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3858,3956,4063,4160,4259,4363,4467,11421", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3951,4058,4155,4254,4358,4462,4579,11517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,82,90,91,94,93,100,92,94,94,90,90,84,103,111,100,104,113,101,168,96,83", "endOffsets": "205,300,407,493,597,716,801,884,975,1067,1162,1256,1357,1450,1545,1640,1731,1822,1907,2011,2123,2224,2329,2443,2545,2714,2811,2895"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1019,1114,1221,1307,1411,1530,1615,1698,1789,1881,1976,2070,2171,2264,2359,2454,2545,2636,2721,2825,2937,3038,3143,3257,3359,3528,11337", "endColumns": "104,94,106,85,103,118,84,82,90,91,94,93,100,92,94,94,90,90,84,103,111,100,104,113,101,168,96,83", "endOffsets": "1014,1109,1216,1302,1406,1525,1610,1693,1784,1876,1971,2065,2166,2259,2354,2449,2540,2631,2716,2820,2932,3033,3138,3252,3354,3523,3620,11416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "75,128,132,133,136,137,138", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6771,10703,11106,11189,11522,11691,11778", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "6837,10785,11184,11332,11686,11773,11856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "5596", "endColumns": "131", "endOffsets": "5723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8843,8918,8979,9044,9117,9196,9269,9354,9436", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "8913,8974,9039,9112,9191,9264,9349,9431,9504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,1987,2067,2144,2212,2296,2367,2438,2534,2628,2695,2760,2813,2871,2919,2980,3054,3133,3209,3283,3347,3406,3477,3542,3613,3665,3728,3813,3898", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,74,79,76,67,83,70,70,95,93,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,1982,2062,2139,2207,2291,2362,2433,2529,2623,2690,2755,2808,2866,2914,2975,3049,3128,3204,3278,3342,3401,3472,3537,3608,3660,3723,3808,3893,3949"}, "to": {"startLines": "2,11,16,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,345,635,6947,7028,7110,7190,7297,7404,7474,7541,7632,7724,7789,7860,7923,7995,8070,8150,8227,8295,8379,8450,8521,8617,8711,8778,9509,9562,9620,9668,9729,9803,9882,9958,10032,10096,10155,10226,10291,10362,10414,10477,10562,10647", "endLines": "10,15,20,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,74,79,76,67,83,70,70,95,93,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "340,630,909,7023,7105,7185,7292,7399,7469,7536,7627,7719,7784,7855,7918,7990,8065,8145,8222,8290,8374,8445,8516,8612,8706,8773,8838,9557,9615,9663,9724,9798,9877,9953,10027,10091,10150,10221,10286,10357,10409,10472,10557,10642,10698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "48,49", "startColumns": "4,4", "startOffsets": "3625,3736", "endColumns": "110,121", "endOffsets": "3731,3853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6aa5fd7f2304b1bbe5c05f658d5472\\transformed\\jetified-play-services-base-18.0.1\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4584,4690,4847,4977,5087,5244,5374,5489,5728,5878,5985,6142,6270,6417,6560,6628,6690", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "4685,4842,4972,5082,5239,5369,5484,5591,5873,5980,6137,6265,6412,6555,6623,6685,6766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "76,129,130,131", "startColumns": "4,4,4,4", "startOffsets": "6842,10790,10890,11004", "endColumns": "104,99,113,101", "endOffsets": "6942,10885,10999,11101"}}]}]}