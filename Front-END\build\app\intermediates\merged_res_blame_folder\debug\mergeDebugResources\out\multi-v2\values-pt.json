{"logs": [{"outputFile": "com.example.whatever.app-mergeDebugResources-50:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1cb470bf488be9472303b483cf380aae\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6465,6535,6605,6677,6743,6820,6887,6988,7081", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "6530,6600,6672,6738,6815,6882,6983,7076,7146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "48,49,50,51,52,53,54,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3669,3766,3868,3967,4067,4174,4284,9007", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3761,3863,3962,4062,4169,4279,4399,9103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "46,47", "startColumns": "4,4", "startOffsets": "3438,3547", "endColumns": "108,121", "endOffsets": "3542,3664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "56,109,110,111", "startColumns": "4,4,4,4", "startOffsets": "4474,8375,8474,8586", "endColumns": "114,98,111,105", "endOffsets": "4584,8469,8581,8687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4f3e8c36915ab4eaa73937a6ff71b02\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1763,1839,1915,1982,2068,2144,2218,2310,2398,2462,2526,2579,2637,2685,2746,2811,2873,2939,3009,3073,3134,3200,3265,3331,3384,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1758,1834,1910,1977,2063,2139,2213,2305,2393,2457,2521,2574,2632,2680,2741,2806,2868,2934,3004,3068,3129,3195,3260,3326,3379,3443,3521,3599,3658"}, "to": {"startLines": "2,11,15,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4589,4670,4753,4826,4925,5021,5095,5161,5257,5352,5418,5487,5554,5625,5702,5778,5854,5921,6007,6083,6157,6249,6337,6401,7151,7204,7262,7310,7371,7436,7498,7564,7634,7698,7759,7825,7890,7956,8009,8073,8151,8229", "endLines": "10,14,18,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "330,516,695,4665,4748,4821,4920,5016,5090,5156,5252,5347,5413,5482,5549,5620,5697,5773,5849,5916,6002,6078,6152,6244,6332,6396,6460,7199,7257,7305,7366,7431,7493,7559,7629,7693,7754,7820,7885,7951,8004,8068,8146,8224,8283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "55,108,112,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4404,8288,8692,8771,9108,9277,9364", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "4469,8370,8766,8917,9272,9359,9440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,2923"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1508,1599,1691,1786,1880,1981,2074,2169,2264,2355,2446,2530,2637,2748,2850,2958,3066,3176,3338,8922", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "815,921,1028,1117,1218,1337,1422,1503,1594,1686,1781,1875,1976,2069,2164,2259,2350,2441,2525,2632,2743,2845,2953,3061,3171,3333,3433,9002"}}]}]}