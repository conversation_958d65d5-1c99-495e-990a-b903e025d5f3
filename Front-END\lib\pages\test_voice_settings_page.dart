import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';

class TestVoiceSettingsPage extends StatefulWidget {
  const TestVoiceSettingsPage({super.key});

  @override
  State<TestVoiceSettingsPage> createState() => _TestVoiceSettingsPageState();
}

class _TestVoiceSettingsPageState extends State<TestVoiceSettingsPage> {
  final ApiService _apiService = ApiService();
  Map<String, dynamic> voiceOptions = {};
  bool isLoading = true;
  String? errorMessage;
  String testStatus = "Starting tests...";

  // Current voice preferences
  String selectedLanguage = 'en';
  String selectedGender = 'male';

  @override
  void initState() {
    super.initState();
    _runTests();
  }

  Future<void> _runTests() async {
    setState(() {
      testStatus = "Testing SharedPreferences...";
    });

    // Test 1: SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('test_key', 'test_value');
      final testValue = prefs.getString('test_key');
      
      if (testValue == 'test_value') {
        setState(() {
          testStatus = "✅ SharedPreferences working. Testing API...";
        });
      } else {
        setState(() {
          testStatus = "❌ SharedPreferences test failed";
          errorMessage = "SharedPreferences test failed";
          isLoading = false;
        });
        return;
      }
    } catch (e) {
      setState(() {
        testStatus = "❌ SharedPreferences error: $e";
        errorMessage = "SharedPreferences error: $e";
        isLoading = false;
      });
      return;
    }

    // Test 2: API Connection
    setState(() {
      testStatus = "Testing API connection...";
    });

    try {
      final result = await _apiService.getVoiceOptions();
      if (result['success']) {
        setState(() {
          voiceOptions = result['data']['voice_options'] ?? {};
          testStatus = "✅ All tests passed!";
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = result['error'] ?? 'Failed to load voice options';
          testStatus = "❌ API test failed: ${result['error']}";
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading voice options: $e';
        testStatus = "❌ API error: $e";
        isLoading = false;
      });
    }
  }

  Future<void> _loadSavedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        selectedLanguage = prefs.getString('voice_language') ?? 'en';
        selectedGender = prefs.getString('voice_gender') ?? 'male';
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading preferences: $e';
      });
    }
  }

  Future<void> _savePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('voice_language', selectedLanguage);
      await prefs.setString('voice_gender', selectedGender);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Voice preferences saved!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving preferences: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accentColor = theme.brightness == Brightness.light
        ? const Color.fromARGB(255, 17, 77, 180)
        : const Color.fromARGB(255, 64, 123, 255);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Test Voice Settings'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: accentColor,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Test Status
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: accentColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(testStatus),
                      if (errorMessage != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Error: $errorMessage',
                          style: const TextStyle(color: Colors.red),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              if (!isLoading && errorMessage == null) ...[
                // Voice Options Display
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Available Voice Options',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: accentColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...voiceOptions.entries.map((entry) {
                          final language = entry.key;
                          final voices = entry.value as Map<String, dynamic>;
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Text(
                              '$language: ${voices.keys.join(', ')}',
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Test Preferences
                ElevatedButton(
                  onPressed: _loadSavedPreferences,
                  child: const Text('Test Load Preferences'),
                ),
                
                const SizedBox(height: 8),
                
                ElevatedButton(
                  onPressed: _savePreferences,
                  child: const Text('Test Save Preferences'),
                ),
              ],
              
              if (isLoading)
                const Center(child: CircularProgressIndicator()),
            ],
          ),
        ),
      ),
    );
  }
}
